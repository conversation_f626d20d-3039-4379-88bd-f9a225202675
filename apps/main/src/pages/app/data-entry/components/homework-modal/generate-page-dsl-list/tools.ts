import { cloneDeep, isObject } from 'lodash';
import { GenerateBusinessType } from '../homework-modal.tool';
import { dataConnectorTemplate } from './config';
import { EspActionField, EspActionFieldsMap } from './type';

// 关于在这里产生dsl数据我个人是不建议的，在这里并没法取到lowcode组件的最新meta信息，且无法复用设计器中现成的拖拽生成逻辑
// 但既然产品觉得在进入设计器之前就需要产生dsl，且按照“模版“的概念维护dsl模版，那么还是照做
// 所以在这里，会根据标准场景维护几套静态dsl配置，也就是 以后 会 移动到 数据库里的 dsl 模版
// 依照模版数据组装所需dsl
// 但其实弊端很明显：1.如果dsl结构变动后需要更新模版和处理逻辑 2.无法和设计器复用生成逻辑

export const generatePageDslList = (
  formValues: {
    generateBusinessType: GenerateBusinessType;
  },
  espActionFieldsMap: EspActionFieldsMap,
  lang: string = 'zh_CN',
): any[] => {
  const { generateBusinessType } = formValues;

  if (generateBusinessType === GenerateBusinessType.BLANK) {
    return null;
  }
  if (generateBusinessType === GenerateBusinessType.BASIC_TABLE) {
    return [
      {
        type: 'browse',
        dsl: {
          layout: [],
          hooks: [],
          rules: [],
          variables: [],
          dataConnectors: [],
          globalSetting: {},
        },
      },
      {
        type: 'edit',
        dsl: {
          layout: [],
          hooks: [],
          rules: [],
          variables: [],
          dataConnectors: [],
          globalSetting: {},
        },
      },
    ];
  }
  if (generateBusinessType === GenerateBusinessType.EDITABLE_TABLE) {
    return [
      {
        type: 'design',
        dsl: {
          layout: [],
          hooks: [],
          rules: [],
          variables: [],
          dataConnectors: [],
          globalSetting: {},
        },
      },
    ];
  }
};

// 通过action组装dataConnectors
export const getDataConnectorByAction = (actionData, espActionFields: EspActionField, lang: string = 'zh_CN') => {
  if (!actionData || !espActionFields) return null;
  const dataConnector = cloneDeep(dataConnectorTemplate);
  dataConnector.name = espActionFields.data_name;
  dataConnector.description = espActionFields.description[lang];
  dataConnector['lang']['description'] = espActionFields.description;
  const digi_service = dataConnector?.option?.request?.headers?.find((s) => s.key === 'digi-service');
  if (isObject(digi_service.value)) {
    digi_service.value.prod = actionData.provider;
    digi_service.value.name = actionData.serviceName;
    digi_service.value = JSON.stringify(digi_service.value);
  }
  dataConnector.option.response.meta = espActionFields;
  return dataConnector;
};
