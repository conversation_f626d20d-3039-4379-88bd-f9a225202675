import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { of, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CommonValidators } from '../../../../../common/utils/common.validators';
import { mysqlKeywords } from 'common/config/common.config';
import { NzMessageService } from 'ng-zorro-antd/message';
import { IndividualService } from 'pages/individual/individual.service';
import { AppService } from 'pages/apps/app.service';
import {
  IInfoModal,
  GenerateBusinessAddSourcetype,
  GenerateBusinessType,
  GenerateBusinessOperateType,
  GenerateViewType,
  GenerateFromType,
  HomeWorkType,
  ApiSupportedHomeWorkTypeSet,
} from './homework-modal.tool';
import { HomeworkModalService } from './homework-modal.service';
import { generatePageDslList } from './generate-page-dsl-list/tools';
import { EspActionFieldsMap } from './generate-page-dsl-list/type';

@Component({
  selector: 'app-new-homework-modal',
  templateUrl: './homework-modal.component.html',
  styleUrls: ['./homework-modal.component.less'],
  providers: [HomeworkModalService],
})
export class HomeworkModalComponent implements OnInit {
  GenerateFromType = GenerateFromType;
  GenerateBusinessType = GenerateBusinessType;
  GenerateBusinessOperateType = GenerateBusinessOperateType;
  infoModalVisible: boolean = false;
  isActionModalShow: boolean = false;
  dataForm: FormGroup;
  homeworkTypeLists: any[] = [];
  associationModels: any[] = [];
  navigationModels: any[] = [];
  queryPlanLists: any[] = [];
  coverageBusinessLists: any[] = [];
  demoUrl: string;
  confirmLoading: boolean = false;
  formLang: any;
  destroy$ = new Subject();
  actionData: any;
  navigationFieldInfo = [
    {
      fieldName: 'navigateModelCode',
      defaultValue: null,
      validators: [Validators.required],
    },
    {
      fieldName: 'navigateModelServiceCode',
      defaultValue: null,
    },
    {
      fieldName: 'supportNavigateFlag',
      defaultValue: false,
    },
  ];
  queryPlanFieldInfo = [
    {
      fieldName: 'dataViewCodes',
      defaultValue: null,
      validators: [Validators.required],
    },
  ];
  coverageFieldInfo = [
    {
      fieldName: 'coveragePageCode',
      defaultValue: null,
      validators: [Validators.required],
    },
  ];
  generateDataViewLoading: boolean = false;
  @Input() infoVisible = () => of(null);
  @Output() submit = new EventEmitter();
  @Output() fastGenerateQueryPlan = new EventEmitter();
  constructor(
    private fb: FormBuilder,
    public appService: AppService,
    private translate: TranslateService,
    private message: NzMessageService,
    private queryService: HomeworkModalService,
    private individualService: IndividualService,
  ) {
    const DefaultBusinessType = GenerateBusinessType.BLANK;
    const defaultInfo = HomeWorkType.find((item) => item.key === DefaultBusinessType);
    this.demoUrl = defaultInfo.demoUrl;
  }
  ngOnInit(): void {
    this.infoVisible()
      .pipe(takeUntil(this.destroy$))
      .subscribe((val: IInfoModal) => {
        if (val.extraData?.viewType === GenerateViewType.BUSINESS) {
          console.log(
            '%c 🚀 [Neovim AutoGR Log]: path = src/pages/app/data-entry/components/homework-modal/homework-modal.component.ts, scope = HomeworkModalComponent.ngOnInit, val = ',
            'color: orangered; font-weight: bold;',
            val,
          );
          this.handlePageDataChange(val);
        }
      });
  }

  mysqlKeywordValidator(control: AbstractControl): ValidationErrors | null {
    if (control?.value === '') {
      return null;
    }
    const keywords = mysqlKeywords; // 添加其他的关键字和保留字
    const pattern = /^(?!.*_.*_.*_.*_.*_.*_)[a-z](?!.*__)(?!.*_$)[a-z0-9_]*(?:_[a-z0-9_]*){0,5}$/;
    if (keywords.includes(control?.value?.toUpperCase()) || !pattern.test(control?.value)) {
      return { modelId: true };
    }
    return null;
  }

  handleQueryModelLists(appCode: string) {
    this.queryService.queryAssociationModalListsService(appCode).subscribe((res) => {
      if (res?.code === 0) {
        this.associationModels = res?.data ?? [];
      }
    });
  }

  handleQueryCoverageLists() {
    const values = this.dataForm.getRawValue();
    const { application, modelId, serviceCode } = values ?? {};
    if (application && modelId && serviceCode) {
      this.queryService
        .queryCoverableBusinessListsService({ application, modelCode: modelId, serviceCode })
        .subscribe((res) => {
          if (res?.code === 0) {
            this.coverageBusinessLists = res?.data ?? [];
          }
        });
    }
  }

  handleQueryPlanList() {
    const values = this.dataForm.getRawValue();
    const { modelId, serviceCode, businessCode } = values ?? {};
    if (modelId && serviceCode) {
      this.queryService
        .queryDataviewListByModelService({ modelCode: modelId, serviceCode, businessCode })
        .subscribe((res) => {
          if (res?.code === 0) {
            this.queryPlanLists = res?.data ?? [];
            this.mergeFormData({
              dataViewCodes: res?.data?.map((item: any) => item.code),
            });
          }
        });
    }
  }

  handleQueryNavigationModelList() {
    const values = this.dataForm.getRawValue();
    const { modelId, serviceCode } = values ?? {};
    if (modelId && serviceCode) {
      this.queryService.queryNavigationModalListsService({ modelCode: modelId, serviceCode }).subscribe((res) => {
        if (res?.code === 0) {
          this.navigationModels = res?.data ?? [];
          if (res?.data?.length && res.data.length > 0) {
            this.mergeFormData({
              navigateModelCode: res.data[0].code,
            });
          }
        }
      });
    }
  }

  handlePageDataChange(info: IInfoModal) {
    const { data, extraData, visible } = info;
    if (visible) {
      const { application, businessCode, modelCode, serviceCode, modelType } = data;
      const { from, perspective, fromResourceHeader } = extraData;
      this.buildForm({
        application,
        businessCode,
        modelCode,
        serviceCode,
        modelType,
        from,
        perspective,
        fromResourceHeader,
      });
      if (from === GenerateFromType.MODEL) {
        this.handleQueryModelLists(application);
      }
      this.infoModalVisible = true;
    } else {
      this.handleReset();
    }
  }

  buildForm(info: any) {
    const groupInfo: any = {
      name: [null, [CommonValidators.trim, Validators.required]],
      dependOnGroundEnd: [false],
      authorityPrefix: [false],
      application: [info.application],
      addPageDesignType: [GenerateBusinessType.BLANK],
      addSourceType: [info.perspective],
      workType: [info.from],
    };

    if (info.from === GenerateFromType.API) {
      this.homeworkTypeLists = HomeWorkType.filter((item) => ApiSupportedHomeWorkTypeSet.has(item.key));
      groupInfo.simpleModelCode = [null, [CommonValidators.trim, this.mysqlKeywordValidator]];
      if (info.perspective === GenerateBusinessAddSourcetype.BUSINESS_OBJECT) {
        groupInfo.businessCode = [info.businessCode];
      }
      this.dataForm = this.fb.group(groupInfo);
    } else {
      this.homeworkTypeLists =
        info.modelType === 'api'
          ? HomeWorkType.filter((item) => ApiSupportedHomeWorkTypeSet.has(item.key))
          : HomeWorkType;
      groupInfo.operateType = [GenerateBusinessOperateType.APPEND];
      groupInfo.serviceCode = [info.serviceCode];
      if (info.perspective === GenerateBusinessAddSourcetype.BUSINESS_OBJECT) {
        groupInfo.businessCode = [info.businessCode];

        groupInfo.modelId = [
          { value: info.modelCode ? info.modelCode : null, disabled: !!info.modelCode },
          [CommonValidators.trim, Validators.required],
        ];
      } else {
        if (info.fromResourceHeader) {
          groupInfo.modelId = [
            { value: info.modelCode ? info.modelCode : null, disabled: !!info.modelCode },
            [CommonValidators.trim, Validators.required],
          ];
        } else {
          groupInfo.modelId = [null, [CommonValidators.trim, Validators.required]];
        }
      }
      this.dataForm = this.fb.group(groupInfo);
    }
  }

  handleCancel(): void {
    this.handleReset();
  }

  mergeFormData(info: Record<string, any>) {
    this.dataForm.patchValue(info);
  }

  handlePatchLang(key: any, data: any): void {
    this.mergeFormData({ [key]: data?.value });
    if (data.needLang) {
      this.formLang = {
        ...(this.formLang || {}),
        [key]: data.lang?.value,
      };
    }
  }

  handleReset(): void {
    this.formLang = null;
    this.homeworkTypeLists = [];
    this.associationModels = [];
    this.navigationModels = [];
    this.queryPlanLists = [];
    this.coverageBusinessLists = [];
    this.dataForm.reset();
    const DefaultBusinessType = GenerateBusinessType.BASIC_TABLE;
    const defaultInfo = HomeWorkType.find((item) => item.key === DefaultBusinessType);
    this.demoUrl = defaultInfo.demoUrl;
    this.infoModalVisible = false;
  }

  async batchQueryEspActionFields(actionIdList: string[]): Promise<any> {
    return this.queryService.batchQueryEspActionFields(actionIdList).toPromise();
  }

  async handlePageDslList(values: any): Promise<any[]> {
    let pageDslList = null;
    try {
      const { listActionId, formActionId, addActionId, editActionId, deleteActionId } = values;
      const actionIdList = [listActionId, formActionId, addActionId, editActionId, deleteActionId].filter(Boolean);
      const batchQueryEspActionFieldsRes = await this.batchQueryEspActionFields(Array.from(new Set(actionIdList)));
      console.log('batchQueryEspActionFieldsRes:', batchQueryEspActionFieldsRes.data);
      const espActionFieldsMap = batchQueryEspActionFieldsRes.data.reduce((acc, currentValue) => {
        acc[currentValue.actionId] = currentValue.apiDataFieldMetadataDTO;
        return acc;
      }, {});
      console.log('espActionFieldsMap:', espActionFieldsMap);

      pageDslList = generatePageDslList(values, espActionFieldsMap, this.translate.currentLang);
      console.log('pageDslList:', pageDslList);
    } catch (error) {
      console.log('handleSelectAction error:', error);
    } finally {
      return pageDslList;
    }
  }

  async handleConfirm(): Promise<void> {
    this.confirmLoading = true;
    if (this.dataForm.invalid) {
      for (const i of Object.keys(this.dataForm?.controls)) {
        this.dataForm.controls[i].markAsDirty();
        this.dataForm.controls[i].updateValueAndValidity();
      }
      this.confirmLoading = false;
      return;
    }
    const values = this.dataForm.getRawValue();
    values.lang = this.formLang;
    /**
     * 这边如果是commonApp的话，null怎么传未知，需要确认，之前传的businessSubCode
     * 沿用md-job-modal.component.ts中对authorityPrefix的处理,稍作修改，这边只会是新增，所以不存在code
     */
    values.authorityPrefix = values.authorityPrefix
      ? this.appService.isCommonApp
        ? `${
            this.individualService?.individualCaseApp
              ? this.individualService.sourceApplicationCode
              : this.appService?.selectedApp?.code
          }:DataEntry_`
        : `${
            this.individualService?.individualCaseApp
              ? this.individualService.sourceApplicationCode
              : this.appService?.selectedApp?.code
          }:basicDataEntry`
      : '';

    console.log('values:', values);
    const pageDslList = await this.handlePageDslList(values);

    // 分销的逻辑调整，考虑到后续可能会实现actionId选择，所以本期仅调整，不删除
    const params = {
      templateType: values.addPageDesignType,
      application: values.application,
      name: values.name,
      version: '2.0',
      lang: values.lang,
    };

    if (pageDslList) {
      params['pageDslList'] = pageDslList;
    }

    // this.queryService.generateBusinessService(values).subscribe(
    this.queryService.createPageDesign(params).subscribe(
      (res) => {
        if (res?.code === 0) {
          this.message.success(this.translate.instant('dj-保存成功！'));
          const updateMenuData = {
            businessCode: values.businessCode,
            menuType: 'pageDesign',
            operatorType: 'add',
            navigateUrl: res.data?.dataViewPath,
            addSourceType: values.addSourceType,
          };
          this.submit.emit(updateMenuData);
          this.handleReset();
        }
        this.confirmLoading = false;
      },
      () => {
        this.confirmLoading = false;
      },
    );
  }

  removeFieldByConfigInfo(configInfos = []) {
    configInfos.forEach(({ fieldName }) => {
      if (this.dataForm.controls.hasOwnProperty(fieldName)) {
        this.dataForm.removeControl(fieldName);
      }
    });
  }
  addFieldByConfigInfo(configInfos = []) {
    configInfos.forEach(({ fieldName, defaultValue, validators }) => {
      if (!this.dataForm.controls.hasOwnProperty(fieldName)) {
        this.dataForm.addControl(fieldName, this.fb.control(defaultValue, validators));
      }
    });
  }
  removeNavigationField() {
    this.removeFieldByConfigInfo(this.navigationFieldInfo);
  }
  addNavigationField() {
    this.addFieldByConfigInfo(this.navigationFieldInfo);
  }
  removeQueryPlanField() {
    this.removeFieldByConfigInfo(this.queryPlanFieldInfo);
  }
  addQueryPlanField() {
    this.addFieldByConfigInfo(this.queryPlanFieldInfo);
  }
  rebuildForm() {
    const addPageDesignType = this.dataForm.get('addPageDesignType')?.value;
    if (
      [GenerateBusinessType.QUERY_PLAN_TABLE, GenerateBusinessType.QUERY_PLAN_VERTICAL_TABLE].includes(
        addPageDesignType,
      )
    ) {
      this.removeNavigationField();
      this.addQueryPlanField();
      this.handleQueryPlanList();
    } else if (
      [GenerateBusinessType.CATEGORY_TABLE, GenerateBusinessType.CATEGORY_TABLE_EDITABLE].includes(addPageDesignType)
    ) {
      this.removeQueryPlanField();
      this.addNavigationField();
      this.handleQueryNavigationModelList();
    } else {
      this.removeNavigationField();
      this.removeQueryPlanField();
    }
  }
  handleWorkTypeClick(item: any) {
    this.demoUrl = item.demoUrl;
    this.mergeFormData({ addPageDesignType: item.key });

    this.dataForm.removeControl('listActionId');
    this.dataForm.removeControl('formActionId');
    this.dataForm.removeControl('addActionId');
    this.dataForm.removeControl('editActionId');
    this.dataForm.removeControl('deleteActionId');

    if (item.key !== GenerateBusinessType.BLANK) {
      this.dataForm.addControl('listActionId', this.fb.control(null, [Validators.required]));
      this.dataForm.addControl('addActionId', this.fb.control(null, []));
      this.dataForm.addControl('editActionId', this.fb.control(null, []));
      this.dataForm.addControl('deleteActionId', this.fb.control(null, []));
    }

    if (item.key === GenerateBusinessType.BASIC_TABLE)
      this.dataForm.addControl('formActionId', this.fb.control(null, [Validators.required]));

    this.rebuildForm();
  }
  handleOpenAction(formKey) {
    this.actionData = {
      actionId: this.dataForm.get(formKey).value || '',
      actionName: '',
      useApp: 'true',
      formKey,
    };
    this.isActionModalShow = true;
  }
  handleConfirmAction(info: any) {
    this.mergeFormData({ [this.actionData.formKey]: info?.actionId });
    this.isActionModalShow = false;
  }
  handleOperateTypeChange(value: GenerateBusinessOperateType) {
    if (value === GenerateBusinessOperateType.COVERAGE) {
      this.handleQueryCoverageLists();
      this.addFieldByConfigInfo(this.coverageFieldInfo);
    } else {
      this.removeFieldByConfigInfo(this.coverageFieldInfo);
    }
    this.mergeFormData({
      coveragePageCode: null,
    });
  }
  handleAssociationModelChange(modelCode: string) {
    const modelInfo = this.associationModels?.find((item) => item.code === modelCode);
    if (modelInfo) {
      this.mergeFormData({
        serviceCode: modelInfo.serviceCode,
        coveragePageCode: null,
      });
      const addPageDesignType = this.dataForm.get('addPageDesignType')?.value;
      const operateType = this.dataForm.get('operateType')?.value;

      if (GenerateBusinessOperateType.COVERAGE === operateType) {
        this.handleQueryCoverageLists();
      }

      if (
        [GenerateBusinessType.QUERY_PLAN_TABLE, GenerateBusinessType.QUERY_PLAN_VERTICAL_TABLE].includes(
          addPageDesignType,
        )
      ) {
        this.handleQueryPlanList();
      } else if (
        [GenerateBusinessType.CATEGORY_TABLE, GenerateBusinessType.CATEGORY_TABLE_EDITABLE].includes(addPageDesignType)
      ) {
        this.handleQueryNavigationModelList();
      }
    }
  }

  handleNavigationModelChange(value: string) {
    if (!value) {
      this.mergeFormData({
        navigateModelServiceCode: null,
        supportNavigateFlag: false,
      });
    } else {
      const navigationInfo = this.navigationModels?.find((item) => item.code === value);
      if (navigationInfo) {
        this.mergeFormData({
          navigateModelServiceCode: navigationInfo.serviceCode,
          supportNavigateFlag: true,
        });
      }
    }
  }

  handleFastGenerateDataView() {
    const values = this.dataForm.getRawValue();
    if (!values?.modelId) {
      this.message.error(this.translate.instant('dj-请先选择关联模型！'));
      return;
    }
    this.generateDataViewLoading = true;
    const reqParams = {
      modelId: values?.modelId,
      serviceCode: values?.serviceCode,
      businessCode: values?.businessCode,
      application: values?.application,
      addSourceType: values?.addSourceType,
      operateType: values?.operateType,
    };
    this.queryService.generateDataviewService(reqParams).subscribe(
      (res) => {
        if (res?.code === 0) {
          this.message.success(this.translate.instant('dj-生成成功！'));
          const submitData = {
            businessCode: values.businessCode,
            menuType: 'dataView',
            operatorType: 'add',
            addSourceType: values.addSourceType,
          };
          this.handleQueryPlanList();
          this.fastGenerateQueryPlan.emit(submitData);
        }
        this.generateDataViewLoading = false;
      },
      () => {
        this.generateDataViewLoading = false;
      },
    );
  }
}
