import { GenerateBusinessType } from '../homework-modal.tool';


type PageType = 'browse' | 'edit' | 'design';

export interface PageDsl {
  type: PageType;
  dsl: {
    layout: any[];
    hooks: any[];
    rules: any[];
    variables: any[];
    dataConnectors: any[];
    globalSetting: any;
  };
}

const type generateBusinessType = GenerateBusinessType.;

export interface PageDslTemplateMap {
  [propName in GenerateBusinessType]: PageDsl[];
}

export interface Lang {
  [propName: string]: LangObject;
}

// 多语言对象
export interface LangObject {
  zh_CN: string;
  zh_TW: string;
  en_US?: string;
}

export interface EspActionField {
  data_name: string;
  data_type: string;
  description: LangObject;
  fields: EspActionField[];
  [propsName: string]: any;
}

export type EspActionFieldsMap = {
  [propsName: string]: EspActionField;
};

export interface DataConnector {
  id: string;
  name: string;
  description: string;
  connectType: string;
  runOnPageLoad: boolean;
  lang: Lang;
  option: {
    request: {
      method: string;
      path: string;
      params: any[];
      body: any;
      headers: any[];
      cookies: any[];
    };
    preProcess: {
      type: string;
      script: string;
    };
    postProcess: {
      type: string;
      script: string;
    };
    response: {
      type: 'Esp' | 'Standard';
      meta: any;
    };
  };
  [propsName: string]: any;
}
